/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Canvas container */
#canvas-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Main content */
.content {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.hero-section {
    text-align: center;
    max-width: 800px;
    width: 100%;
}

/* Logo */
.logo {
    margin-bottom: 3rem;
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.5s forwards;
}

.logo-text {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    letter-spacing: 0.3em;
    background: linear-gradient(45deg, #00f5ff, #ff00ff, #ffff00);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.logo-subtitle {
    font-size: 0.9rem;
    color: #888;
    margin-top: 0.5rem;
    letter-spacing: 0.1em;
}

/* Main title */
.title {
    font-family: 'Orbitron', monospace;
    font-size: clamp(2.5rem, 8vw, 6rem);
    font-weight: 700;
    line-height: 0.9;
    margin-bottom: 2rem;
    overflow: hidden;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(100px);
    animation: slideInUp 0.8s ease-out forwards;
}

.title-line:nth-child(1) { animation-delay: 1s; }
.title-line:nth-child(2) { animation-delay: 1.2s; }
.title-line:nth-child(3) { animation-delay: 1.4s; }

/* Description */
.description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #ccc;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0;
    animation: fadeInUp 1s ease-out 1.8s forwards;
}

/* Countdown */
.countdown {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    opacity: 0;
    animation: fadeInUp 1s ease-out 2s forwards;
}

.countdown-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    min-width: 80px;
}

.countdown-number {
    display: block;
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: #00f5ff;
    text-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
}

.countdown-label {
    font-size: 0.8rem;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-top: 0.5rem;
}

/* CTA Section */
.cta-section {
    opacity: 0;
    animation: fadeInUp 1s ease-out 2.2s forwards;
}

.notify-btn {
    position: relative;
    background: linear-gradient(45deg, #00f5ff, #ff00ff);
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-family: 'Orbitron', monospace;
    font-size: 1rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.notify-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.notify-btn:hover .btn-glow {
    left: 100%;
}

/* Social links */
.social-links {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

.social-link {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #888;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    color: #00f5ff;
    border-color: #00f5ff;
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
    transform: translateY(-2px);
}

.social-link svg {
    width: 20px;
    height: 20px;
}

/* Loading indicator */
.loading-indicator {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    text-align: center;
    opacity: 1;
    transition: opacity 1s ease;
}

.loading-bar {
    width: 200px;
    height: 2px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00f5ff, #ff00ff);
    width: 0%;
    transition: width 0.3s ease;
}

.loading-text {
    font-size: 0.8rem;
    color: #888;
    letter-spacing: 0.1em;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(20, 20, 20, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    backdrop-filter: blur(20px);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    font-family: 'Orbitron', monospace;
    color: #00f5ff;
}

.modal-close {
    background: none;
    border: none;
    color: #888;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #fff;
}

.modal-body p {
    margin-bottom: 1.5rem;
    color: #ccc;
}

#emailForm {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

#emailForm input {
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 1rem;
}

#emailForm input:focus {
    outline: none;
    border-color: #00f5ff;
    box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

#emailForm button {
    padding: 1rem;
    background: linear-gradient(45deg, #00f5ff, #ff00ff);
    border: none;
    border-radius: 10px;
    color: #000;
    font-weight: 700;
    transition: transform 0.3s ease;
}

#emailForm button:hover {
    transform: translateY(-2px);
}

/* Animations */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Responsive design */
@media (max-width: 768px) {
    .countdown {
        gap: 1rem;
    }
    
    .countdown-item {
        min-width: 60px;
        padding: 0.8rem;
    }
    
    .countdown-number {
        font-size: 1.5rem;
    }
    
    .social-links {
        gap: 1rem;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
    }
    
    .social-link svg {
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 480px) {
    .content {
        padding: 1rem;
    }
    
    .countdown {
        flex-wrap: wrap;
        gap: 0.8rem;
    }
    
    .description {
        font-size: 1rem;
    }
    
    .notify-btn {
        padding: 0.8rem 2rem;
        font-size: 0.9rem;
    }
}
