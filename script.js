// Three.js Scene Setup
let scene, camera, renderer, particles, particleSystem;
let geometricShapes = [];
let flowingLines = [];
let backgroundMesh, tunnelMesh;
let mouseX = 0, mouseY = 0;
let windowHalfX = window.innerWidth / 2;
let windowHalfY = window.innerHeight / 2;
let clock = new THREE.Clock();

// Initialize Three.js
function initThree() {
    // Scene
    scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x000000, 500, 2000);

    // Camera
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 1, 3000);
    camera.position.z = 500;

    // Renderer
    renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('canvas-container').appendChild(renderer.domElement);

    // Create all visual elements
    createBackgroundGradient();
    createTunnelEffect();
    createParticleSystem();
    createGeometricShapes();
    createFlowingLines();

    // Add enhanced lighting
    setupLighting();

    // Start animation
    animate();
}

// Create animated background gradient
function createBackgroundGradient() {
    const geometry = new THREE.PlaneGeometry(4000, 4000);
    const material = new THREE.ShaderMaterial({
        uniforms: {
            time: { value: 0 },
            resolution: { value: new THREE.Vector2(window.innerWidth, window.innerHeight) }
        },
        vertexShader: `
            void main() {
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: `
            precision highp float;
            uniform float time;
            uniform vec2 resolution;

            void main() {
                vec2 uv = gl_FragCoord.xy / resolution.xy;

                // Create flowing gradient
                float wave1 = sin(uv.x * 3.0 + time * 0.001) * 0.5 + 0.5;
                float wave2 = cos(uv.y * 2.0 + time * 0.0015) * 0.5 + 0.5;
                float wave3 = sin((uv.x + uv.y) * 1.5 + time * 0.0008) * 0.5 + 0.5;

                vec3 color1 = vec3(0.0, 0.1, 0.3); // Deep blue
                vec3 color2 = vec3(0.1, 0.0, 0.2); // Deep purple
                vec3 color3 = vec3(0.0, 0.2, 0.1); // Deep green

                vec3 finalColor = mix(color1, color2, wave1);
                finalColor = mix(finalColor, color3, wave2 * wave3);

                gl_FragColor = vec4(finalColor, 0.3);
            }
        `,
        transparent: true,
        side: THREE.DoubleSide
    });

    backgroundMesh = new THREE.Mesh(geometry, material);
    backgroundMesh.position.z = -1000;
    scene.add(backgroundMesh);
}

// Create tunnel effect
function createTunnelEffect() {
    const geometry = new THREE.CylinderGeometry(800, 800, 2000, 32, 1, true);
    const material = new THREE.ShaderMaterial({
        uniforms: {
            time: { value: 0 }
        },
        vertexShader: `
            varying vec2 vUv;
            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: `
            precision highp float;
            uniform float time;
            varying vec2 vUv;

            void main() {
                float rings = sin(vUv.y * 20.0 + time * 0.002) * 0.5 + 0.5;
                float spiral = sin(vUv.x * 50.0 + vUv.y * 10.0 + time * 0.003) * 0.5 + 0.5;

                vec3 color = vec3(0.0, 0.96, 1.0) * rings * 0.1;
                color += vec3(1.0, 0.0, 1.0) * spiral * 0.05;

                float alpha = (rings + spiral) * 0.1;
                gl_FragColor = vec4(color, alpha);
            }
        `,
        transparent: true,
        side: THREE.BackSide,
        blending: THREE.AdditiveBlending
    });

    tunnelMesh = new THREE.Mesh(geometry, material);
    tunnelMesh.rotation.x = Math.PI / 2;
    scene.add(tunnelMesh);
}

// Enhanced particle system
function createParticleSystem() {
    const particleCount = 3000;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const velocities = new Float32Array(particleCount * 3);

    // Enhanced color palette
    const colorPalette = [
        new THREE.Color(0x00f5ff), // Cyan
        new THREE.Color(0xff00ff), // Magenta
        new THREE.Color(0xffff00), // Yellow
        new THREE.Color(0x00ff88), // Mint
        new THREE.Color(0xff0080), // Pink
        new THREE.Color(0x8000ff), // Purple
        new THREE.Color(0xff8000), // Orange
    ];

    for (let i = 0; i < particleCount; i++) {
        // Position in spherical distribution
        const radius = Math.random() * 1500 + 200;
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.random() * Math.PI;

        positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[i * 3 + 2] = radius * Math.cos(phi);

        // Velocity for floating motion
        velocities[i * 3] = (Math.random() - 0.5) * 0.5;
        velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.5;
        velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.5;

        // Color
        const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];
        colors[i * 3] = color.r;
        colors[i * 3 + 1] = color.g;
        colors[i * 3 + 2] = color.b;

        // Size
        sizes[i] = Math.random() * 4 + 1;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('particleColor', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

    // Enhanced particle material with glow effect
    const material = new THREE.ShaderMaterial({
        uniforms: {
            time: { value: 0 },
            pixelRatio: { value: window.devicePixelRatio }
        },
        vertexShader: `
            precision highp float;
            attribute float size;
            attribute vec3 particleColor;
            attribute vec3 velocity;
            varying vec3 vColor;
            uniform float time;

            void main() {
                vColor = particleColor;
                vec3 pos = position;

                // Add floating motion
                pos += velocity * time * 0.0001;

                // Add wave motion
                pos.x += sin(time * 0.001 + position.y * 0.01) * 30.0;
                pos.y += cos(time * 0.0015 + position.x * 0.01) * 25.0;
                pos.z += sin(time * 0.0008 + position.x * 0.005 + position.y * 0.005) * 20.0;

                vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
                gl_PointSize = size * (400.0 / -mvPosition.z);
                gl_Position = projectionMatrix * mvPosition;
            }
        `,
        fragmentShader: `
            precision highp float;
            varying vec3 vColor;

            void main() {
                vec2 center = gl_PointCoord - vec2(0.5);
                float distanceToCenter = length(center);

                // Create glow effect
                float glow = 1.0 - smoothstep(0.0, 0.5, distanceToCenter);
                float core = 1.0 - smoothstep(0.0, 0.2, distanceToCenter);

                vec3 finalColor = vColor * (glow * 0.6 + core * 0.4);
                float alpha = glow * 0.9;

                gl_FragColor = vec4(finalColor, alpha);
            }
        `,
        transparent: true,
        vertexColors: true,
        blending: THREE.AdditiveBlending,
        depthWrite: false
    });

    particleSystem = new THREE.Points(geometry, material);
    scene.add(particleSystem);
}

// Create geometric shapes
function createGeometricShapes() {
    const shapeCount = 15;

    for (let i = 0; i < shapeCount; i++) {
        let geometry, material;
        const shapeType = Math.floor(Math.random() * 4);

        // Different geometric shapes
        switch (shapeType) {
            case 0: // Torus
                geometry = new THREE.TorusGeometry(20 + Math.random() * 30, 5 + Math.random() * 10, 8, 16);
                break;
            case 1: // Octahedron
                geometry = new THREE.OctahedronGeometry(15 + Math.random() * 25);
                break;
            case 2: // Dodecahedron
                geometry = new THREE.DodecahedronGeometry(15 + Math.random() * 20);
                break;
            case 3: // Icosahedron
                geometry = new THREE.IcosahedronGeometry(15 + Math.random() * 25);
                break;
        }

        material = new THREE.MeshPhongMaterial({
            color: new THREE.Color().setHSL(Math.random(), 0.8, 0.5),
            transparent: true,
            opacity: 0.3,
            wireframe: Math.random() > 0.5
        });

        const mesh = new THREE.Mesh(geometry, material);

        // Random position
        mesh.position.set(
            (Math.random() - 0.5) * 1500,
            (Math.random() - 0.5) * 1500,
            (Math.random() - 0.5) * 800
        );

        // Random rotation
        mesh.rotation.set(
            Math.random() * Math.PI,
            Math.random() * Math.PI,
            Math.random() * Math.PI
        );

        // Store rotation speed
        mesh.userData = {
            rotationSpeed: {
                x: (Math.random() - 0.5) * 0.02,
                y: (Math.random() - 0.5) * 0.02,
                z: (Math.random() - 0.5) * 0.02
            }
        };

        geometricShapes.push(mesh);
        scene.add(mesh);
    }
}

// Create flowing lines
function createFlowingLines() {
    const lineCount = 8;

    for (let i = 0; i < lineCount; i++) {
        const points = [];
        const pointCount = 50;

        // Create curved line points
        for (let j = 0; j < pointCount; j++) {
            const t = j / (pointCount - 1);
            const x = (t - 0.5) * 2000 + Math.sin(t * Math.PI * 4) * 200;
            const y = Math.sin(t * Math.PI * 2) * 300 + (Math.random() - 0.5) * 100;
            const z = Math.cos(t * Math.PI * 3) * 400 + (Math.random() - 0.5) * 200;

            points.push(new THREE.Vector3(x, y, z));
        }

        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({
            color: new THREE.Color().setHSL(i / lineCount, 1, 0.5),
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });

        const line = new THREE.Line(geometry, material);
        line.userData = { originalPoints: points.slice() };

        flowingLines.push(line);
        scene.add(line);
    }
}

// Setup enhanced lighting
function setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    scene.add(ambientLight);

    // Multiple colored point lights
    const lights = [
        { color: 0x00f5ff, position: [300, 300, 300] },
        { color: 0xff00ff, position: [-300, -300, 300] },
        { color: 0xffff00, position: [300, -300, -300] },
        { color: 0x00ff88, position: [-300, 300, -300] }
    ];

    lights.forEach(lightData => {
        const light = new THREE.PointLight(lightData.color, 0.8, 1500);
        light.position.set(...lightData.position);
        light.castShadow = true;
        scene.add(light);
    });

    // Directional light for overall illumination
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(100, 100, 50);
    directionalLight.castShadow = true;
    scene.add(directionalLight);
}

// Enhanced animation loop
function animate() {
    requestAnimationFrame(animate);

    const time = Date.now();
    const elapsedTime = clock.getElapsedTime();

    // Update background gradient
    if (backgroundMesh) {
        backgroundMesh.material.uniforms.time.value = time;
    }

    // Update tunnel effect
    if (tunnelMesh) {
        tunnelMesh.material.uniforms.time.value = time;
        tunnelMesh.rotation.z += 0.001;
    }

    // Update particle system
    if (particleSystem) {
        particleSystem.material.uniforms.time.value = time;
        particleSystem.rotation.x += 0.0003;
        particleSystem.rotation.y += 0.0005;

        // Mouse interaction
        particleSystem.rotation.x += (mouseY * 0.00001 - particleSystem.rotation.x) * 0.05;
        particleSystem.rotation.y += (mouseX * 0.00001 - particleSystem.rotation.y) * 0.05;
    }

    // Update geometric shapes
    geometricShapes.forEach(shape => {
        shape.rotation.x += shape.userData.rotationSpeed.x;
        shape.rotation.y += shape.userData.rotationSpeed.y;
        shape.rotation.z += shape.userData.rotationSpeed.z;

        // Add floating motion
        shape.position.y += Math.sin(elapsedTime + shape.position.x * 0.001) * 0.5;
    });

    // Update flowing lines
    flowingLines.forEach((line, index) => {
        const positions = line.geometry.attributes.position.array;
        const originalPoints = line.userData.originalPoints;

        for (let i = 0; i < originalPoints.length; i++) {
            const point = originalPoints[i];
            const offset = Math.sin(elapsedTime * 2 + i * 0.1 + index) * 20;

            positions[i * 3] = point.x + offset;
            positions[i * 3 + 1] = point.y + Math.cos(elapsedTime + i * 0.1) * 15;
            positions[i * 3 + 2] = point.z + Math.sin(elapsedTime * 1.5 + i * 0.05) * 10;
        }

        line.geometry.attributes.position.needsUpdate = true;
    });

    // Enhanced camera movement
    const targetX = mouseX * 0.0002;
    const targetY = mouseY * 0.0002;

    camera.position.x += (targetX - camera.position.x) * 0.02;
    camera.position.y += (-targetY - camera.position.y) * 0.02;

    // Add subtle camera rotation
    camera.rotation.z = Math.sin(elapsedTime * 0.1) * 0.02;

    camera.lookAt(scene.position);

    renderer.render(scene, camera);
}

// Mouse movement handler
function onDocumentMouseMove(event) {
    mouseX = event.clientX - windowHalfX;
    mouseY = event.clientY - windowHalfY;
}

// Window resize handler
function onWindowResize() {
    windowHalfX = window.innerWidth / 2;
    windowHalfY = window.innerHeight / 2;

    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();

    renderer.setSize(window.innerWidth, window.innerHeight);

    // Update background gradient resolution
    if (backgroundMesh) {
        backgroundMesh.material.uniforms.resolution.value.set(window.innerWidth, window.innerHeight);
    }
}

// Countdown timer
function initCountdown() {
    // Set launch date (30 days from now)
    const launchDate = new Date();
    launchDate.setDate(launchDate.getDate() + 30);
    
    function updateCountdown() {
        const now = new Date().getTime();
        const distance = launchDate.getTime() - now;
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        document.getElementById('days').textContent = days.toString().padStart(2, '0');
        document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
        document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
        document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        
        if (distance < 0) {
            document.getElementById('countdown').innerHTML = '<div class="countdown-item"><span class="countdown-number">LIVE</span></div>';
        }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

// Loading animation
function initLoading() {
    const loadingProgress = document.getElementById('loadingProgress');
    const loadingIndicator = document.querySelector('.loading-indicator');
    let progress = 0;
    
    const loadingInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(loadingInterval);
            
            setTimeout(() => {
                loadingIndicator.style.opacity = '0';
                setTimeout(() => {
                    loadingIndicator.style.display = 'none';
                }, 1000);
            }, 500);
        }
        
        loadingProgress.style.width = progress + '%';
    }, 200);
}

// Modal functionality
function initModal() {
    const notifyBtn = document.getElementById('notifyBtn');
    const modal = document.getElementById('emailModal');
    const modalClose = document.getElementById('modalClose');
    const emailForm = document.getElementById('emailForm');
    
    notifyBtn.addEventListener('click', () => {
        modal.style.display = 'block';
        setTimeout(() => {
            modal.style.opacity = '1';
        }, 10);
    });
    
    modalClose.addEventListener('click', closeModal);
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    function closeModal() {
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
    
    emailForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const email = e.target.querySelector('input[type="email"]').value;
        
        // Simulate email subscription
        alert(`Thank you! We'll notify you at ${email} when the portfolio launches.`);
        closeModal();
        e.target.reset();
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initThree();
    initCountdown();
    initLoading();
    initModal();
    
    // Event listeners
    document.addEventListener('mousemove', onDocumentMouseMove, false);
    window.addEventListener('resize', onWindowResize, false);
    
    // Add some interactive effects
    document.querySelectorAll('.social-link').forEach(link => {
        link.addEventListener('mouseenter', () => {
            if (particleSystem) {
                particleSystem.material.uniforms.time.value += 100;
            }
        });
    });
});

// Add custom cursor and visual effects
function addVisualEffects() {
    // Main cursor
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    cursor.style.cssText = `
        position: fixed;
        width: 20px;
        height: 20px;
        background: radial-gradient(circle, rgba(0,245,255,1) 0%, rgba(0,245,255,0.3) 50%, transparent 100%);
        border: 2px solid rgba(0,245,255,0.8);
        border-radius: 50%;
        pointer-events: none;
        z-index: 10000;
        mix-blend-mode: screen;
        transform: translate(-50%, -50%);
        will-change: transform;
    `;
    document.body.appendChild(cursor);

    // Cursor trail
    const trail = document.createElement('div');
    trail.className = 'cursor-trail';
    trail.style.cssText = `
        position: fixed;
        width: 40px;
        height: 40px;
        background: radial-gradient(circle, transparent 30%, rgba(255,0,255,0.2) 50%, transparent 100%);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        mix-blend-mode: screen;
        transform: translate(-50%, -50%);
        will-change: transform;
    `;
    document.body.appendChild(trail);

    // Outer glow
    const glow = document.createElement('div');
    glow.className = 'cursor-glow';
    glow.style.cssText = `
        position: fixed;
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, transparent 60%, rgba(255,255,0,0.1) 80%, transparent 100%);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9998;
        mix-blend-mode: screen;
        transform: translate(-50%, -50%);
        will-change: transform;
    `;
    document.body.appendChild(glow);

    let mouseX = 0, mouseY = 0;
    let trailX = 0, trailY = 0;
    let glowX = 0, glowY = 0;
    let isAnimating = false;

    // Mouse movement handler with throttling
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // Update main cursor immediately using transform for better performance
        cursor.style.transform = `translate(${mouseX - 10}px, ${mouseY - 10}px)`;

        // Start animation loop if not already running
        if (!isAnimating) {
            isAnimating = true;
            animateTrail();
        }
    });

    // Smooth trail animation with optimized performance
    function animateTrail() {
        // Smooth interpolation
        trailX += (mouseX - trailX) * 0.15;
        trailY += (mouseY - trailY) * 0.15;

        glowX += (mouseX - glowX) * 0.08;
        glowY += (mouseY - glowY) * 0.08;

        // Apply scaling based on interaction state
        let trailScale = 1;
        if (isClicking) {
            trailScale = 1.5;
        } else if (isHovering) {
            trailScale = 1.2;
        }

        // Use transform instead of left/top for better performance
        trail.style.transform = `translate(${trailX - 20}px, ${trailY - 20}px) scale(${trailScale})`;
        glow.style.transform = `translate(${glowX - 40}px, ${glowY - 40}px)`;

        // Continue animation if cursor is still moving
        const trailDistance = Math.abs(mouseX - trailX) + Math.abs(mouseY - trailY);
        const glowDistance = Math.abs(mouseX - glowX) + Math.abs(mouseY - glowY);

        if (trailDistance > 0.5 || glowDistance > 0.5) {
            requestAnimationFrame(animateTrail);
        } else {
            isAnimating = false;
        }
    }

    // Hover effects for interactive elements with smooth transitions
    const interactiveElements = document.querySelectorAll('button, a, .social-link, .countdown-item');
    let isHovering = false;
    let isClicking = false;

    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            isHovering = true;
            updateCursorTransform();

            cursor.style.background = 'radial-gradient(circle, rgba(255,0,255,1) 0%, rgba(255,0,255,0.3) 50%, transparent 100%)';
            cursor.style.borderColor = 'rgba(255,0,255,0.8)';
            cursor.style.transition = 'background 0.2s ease, border-color 0.2s ease, transform 0.2s ease';

            trail.style.background = 'radial-gradient(circle, transparent 30%, rgba(0,245,255,0.3) 50%, transparent 100%)';
            trail.style.transition = 'background 0.2s ease, transform 0.2s ease';
        });

        element.addEventListener('mouseleave', () => {
            isHovering = false;
            updateCursorTransform();

            cursor.style.background = 'radial-gradient(circle, rgba(0,245,255,1) 0%, rgba(0,245,255,0.3) 50%, transparent 100%)';
            cursor.style.borderColor = 'rgba(0,245,255,0.8)';

            trail.style.background = 'radial-gradient(circle, transparent 30%, rgba(255,0,255,0.2) 50%, transparent 100%)';
        });
    });

    // Click effect with smooth scaling
    document.addEventListener('mousedown', () => {
        isClicking = true;
        updateCursorTransform();
    });

    document.addEventListener('mouseup', () => {
        isClicking = false;
        updateCursorTransform();
    });

    // Function to update cursor transform based on state
    function updateCursorTransform() {
        let scale = 1;

        if (isClicking) {
            scale = 0.8;
        } else if (isHovering) {
            scale = 1.5;
        }

        cursor.style.transform = `translate(${mouseX - 10}px, ${mouseY - 10}px) scale(${scale})`;
        // Trail transform with scaling is handled in the animation loop
    }
}

// Initialize visual effects
setTimeout(addVisualEffects, 1000);
